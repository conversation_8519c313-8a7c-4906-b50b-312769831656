{% extends 'base.html' %}
{% load django_bootstrap5 %}

{% block title %}إضافة عضو{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">إضافة عضو جديد</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">الاسم الكامل *</label>
                            <input type="text" class="form-control" id="name" name="name"
                                   value="{{ form_data.name|default:'' }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="national_id" class="form-label">رقم الهوية *</label>
                            <input type="text" class="form-control" id="national_id" name="national_id"
                                   value="{{ form_data.national_id|default:'' }}"
                                   pattern="[0-9]{9}" title="رقم الهوية يجب أن يكون 9 أرقام" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الجوال *</label>
                            <input type="tel" class="form-control" id="phone" name="phone"
                                   value="{{ form_data.phone|default:'' }}"
                                   pattern="05[0-9]{8}" title="رقم الجوال يجب أن يبدأ بـ 05 ويتكون من 10 أرقام" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="{{ form_data.email|default:'' }}">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3">{{ form_data.address|default:'' }}</textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">حالة الاشتراك *</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="active" {% if form_data.status == 'active' or not form_data.status %}selected{% endif %}>نشط</option>
                                <option value="inactive" {% if form_data.status == 'inactive' %}selected{% endif %}>غير نشط</option>
                                <option value="suspended" {% if form_data.status == 'suspended' %}selected{% endif %}>موقوف</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="joined_at" class="form-label">تاريخ الانضمام *</label>
                            <input type="date" class="form-control" id="joined_at" name="joined_at"
                                   value="{% if form_data.joined_at %}{{ form_data.joined_at }}{% else %}{% now 'Y-m-d' %}{% endif %}" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3">{{ form_data.notes|default:'' }}</textarea>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> إضافة العضو
                        </button>
                        <a href="{% url 'members' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validate national ID
    const nationalIdInput = document.getElementById('national_id');
    nationalIdInput.addEventListener('input', function() {
        const value = this.value;
        if (value.length > 0 && !/^[0-9]*$/.test(value)) {
            this.setCustomValidity('رقم الهوية يجب أن يحتوي على أرقام فقط');
        } else if (value.length > 0 && value.length !== 10) {
            this.setCustomValidity('رقم الهوية يجب أن يكون 9 أرقام');
        } else {
            this.setCustomValidity('');
        }
    });

    // Validate phone number
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function() {
        const value = this.value;
        if (value.length > 0 && !/^05[0-9]*$/.test(value)) {
            this.setCustomValidity('رقم الجوال يجب أن يبدأ بـ 05');
        } else if (value.length > 0 && value.length !== 10) {
            this.setCustomValidity('رقم الجوال يجب أن يكون 10 أرقام');
        } else {
            this.setCustomValidity('');
        }
    });
});
</script>
{% endblock %}
