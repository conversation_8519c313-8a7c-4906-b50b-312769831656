#!/usr/bin/env python
"""
Enhanced Project Management Script for Tadamon Cooperative Management Platform
This script provides various utilities for managing the project
"""

import os
import sys
import django
import subprocess
from django.core.management import execute_from_command_line
from django.contrib.auth import get_user_model

def setup_environment():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tadamon.settings')
    django.setup()

def print_header(title):
    """Print a formatted header"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_success(message):
    """Print success message"""
    print(f"✅ {message}")

def print_error(message):
    """Print error message"""
    print(f"❌ {message}")

def print_info(message):
    """Print info message"""
    print(f"ℹ️  {message}")

def setup_database():
    """Setup database with migrations and initial data"""
    print_header("إعداد قاعدة البيانات")
    
    try:
        print_info("إنشاء migrations...")
        execute_from_command_line(['manage.py', 'makemigrations'])
        
        print_info("تطبيق migrations...")
        execute_from_command_line(['manage.py', 'migrate'])
        
        print_success("تم إعداد قاعدة البيانات بنجاح")
        return True
    except Exception as e:
        print_error(f"فشل في إعداد قاعدة البيانات: {e}")
        return False

def create_superuser():
    """Create superuser if not exists"""
    print_header("إنشاء المستخدم الإداري")
    
    try:
        User = get_user_model()
        
        if User.objects.filter(username='admin').exists():
            print_info("المستخدم الإداري موجود بالفعل")
            return True
        
        User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123'
        )
        print_success("تم إنشاء المستخدم الإداري: admin / admin123")
        return True
    except Exception as e:
        print_error(f"فشل في إنشاء المستخدم الإداري: {e}")
        return False

def create_sample_data():
    """Create sample data for testing"""
    print_header("إنشاء بيانات تجريبية")
    
    try:
        from core.models import Association, Member, Payment
        from django.utils import timezone
        from datetime import timedelta
        import random
        
        # Create association if not exists
        association, created = Association.objects.get_or_create(
            name='جمعية تضامن التعاونية',
            defaults={
                'description': 'جمعية تعاونية لخدمة المجتمع المحلي',
                'address': 'فلسطين',
                'phone': '0112345678',
                'email': '<EMAIL>',
                'registration_number': 'TADAMON001',
                'established_date': timezone.now().date(),
                'monthly_contribution': 50.00
            }
        )
        
        if created:
            print_success("تم إنشاء الجمعية الافتراضية")
        
        # Create sample members
        sample_members = [
            {'name': 'أحمد محمد العلي', 'national_id': '1234567890', 'phone': '0501234567'},
            {'name': 'فاطمة عبدالله السالم', 'national_id': '1234567891', 'phone': '0501234568'},
            {'name': 'محمد عبدالعزيز الأحمد', 'national_id': '1234567892', 'phone': '0501234569'},
            {'name': 'نورا سعد المطيري', 'national_id': '1234567893', 'phone': '0501234570'},
            {'name': 'خالد فهد الشمري', 'national_id': '1234567894', 'phone': '0501234571'},
        ]
        
        members_created = 0
        for member_data in sample_members:
            member, created = Member.objects.get_or_create(
                national_id=member_data['national_id'],
                defaults={
                    'association': association,
                    'name': member_data['name'],
                    'phone': member_data['phone'],
                    'email': f"{member_data['name'].split()[0].lower()}@example.com",
                    'address': 'الرياض، المملكة العربية السعودية',
                    'status': 'active',
                    'joined_at': timezone.now().date() - timedelta(days=random.randint(30, 365)),
                    'notes': 'عضو تجريبي'
                }
            )
            if created:
                members_created += 1
        
        print_success(f"تم إنشاء {members_created} عضو جديد")
        
        # Create sample payments
        members = Member.objects.all()
        payments_created = 0
        
        for member in members:
            # Create 1-3 payments for each member
            num_payments = random.randint(1, 3)
            for i in range(num_payments):
                payment_date = timezone.now().date() - timedelta(days=random.randint(1, 90))
                receipt_number = f"REC{member.id:03d}{i+1:02d}"
                
                payment, created = Payment.objects.get_or_create(
                    receipt_number=receipt_number,
                    defaults={
                        'member': member,
                        'amount': random.choice([50.00, 75.00, 100.00]),
                        'date': payment_date,
                        'payment_type': 'monthly',
                        'month': payment_date.replace(day=1),
                        'notes': 'دفعة تجريبية',
                        'created_by': None
                    }
                )
                if created:
                    payments_created += 1
        
        print_success(f"تم إنشاء {payments_created} دفعة جديدة")
        return True
        
    except Exception as e:
        print_error(f"فشل في إنشاء البيانات التجريبية: {e}")
        return False

def collect_static():
    """Collect static files"""
    print_header("جمع الملفات الثابتة")
    
    try:
        execute_from_command_line(['manage.py', 'collectstatic', '--noinput'])
        print_success("تم جمع الملفات الثابتة")
        return True
    except Exception as e:
        print_error(f"فشل في جمع الملفات الثابتة: {e}")
        return False

def run_tests():
    """Run project tests"""
    print_header("تشغيل الاختبارات")
    
    try:
        execute_from_command_line(['manage.py', 'test'])
        print_success("تم تشغيل الاختبارات بنجاح")
        return True
    except Exception as e:
        print_error(f"فشل في تشغيل الاختبارات: {e}")
        return False

def check_project():
    """Check project for issues"""
    print_header("فحص المشروع")
    
    try:
        execute_from_command_line(['manage.py', 'check'])
        print_success("لا توجد مشاكل في المشروع")
        return True
    except Exception as e:
        print_error(f"توجد مشاكل في المشروع: {e}")
        return False

def show_menu():
    """Show main menu"""
    print_header("إدارة مشروع تضامن")
    print("1. إعداد قاعدة البيانات")
    print("2. إنشاء المستخدم الإداري")
    print("3. إنشاء بيانات تجريبية")
    print("4. جمع الملفات الثابتة")
    print("5. فحص المشروع")
    print("6. تشغيل الاختبارات")
    print("7. إعداد كامل (جميع الخطوات)")
    print("8. تشغيل الخادم")
    print("0. خروج")
    print("-" * 60)

def full_setup():
    """Run full project setup"""
    print_header("الإعداد الكامل للمشروع")
    
    steps = [
        ("فحص المشروع", check_project),
        ("إعداد قاعدة البيانات", setup_database),
        ("إنشاء المستخدم الإداري", create_superuser),
        ("إنشاء بيانات تجريبية", create_sample_data),
        ("جمع الملفات الثابتة", collect_static),
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        if step_func():
            success_count += 1
        else:
            print_error(f"فشل في: {step_name}")
    
    print_header("نتائج الإعداد")
    print(f"تم بنجاح: {success_count}/{len(steps)} خطوات")
    
    if success_count == len(steps):
        print_success("تم الإعداد الكامل بنجاح!")
        print_info("يمكنك الآن تشغيل الخادم باستخدام: python manage.py runserver")
        print_info("رابط الموقع: http://127.0.0.1:8000")
        print_info("لوحة الإدارة: http://127.0.0.1:8000/admin")
        print_info("المستخدم: admin | كلمة المرور: admin123")
    else:
        print_error("فشل في بعض خطوات الإعداد")

def run_server():
    """Run development server"""
    print_header("تشغيل خادم التطوير")
    print_info("سيتم تشغيل الخادم على: http://127.0.0.1:8000")
    print_info("اضغط Ctrl+C لإيقاف الخادم")
    
    try:
        execute_from_command_line(['manage.py', 'runserver'])
    except KeyboardInterrupt:
        print_info("تم إيقاف الخادم")

def main():
    """Main function"""
    setup_environment()
    
    while True:
        show_menu()
        choice = input("اختر رقم العملية: ").strip()
        
        if choice == '1':
            setup_database()
        elif choice == '2':
            create_superuser()
        elif choice == '3':
            create_sample_data()
        elif choice == '4':
            collect_static()
        elif choice == '5':
            check_project()
        elif choice == '6':
            run_tests()
        elif choice == '7':
            full_setup()
        elif choice == '8':
            run_server()
        elif choice == '0':
            print_info("شكراً لاستخدام منصة تضامن!")
            break
        else:
            print_error("اختيار غير صحيح، حاول مرة أخرى")
        
        input("\nاضغط Enter للمتابعة...")

if __name__ == '__main__':
    main()
